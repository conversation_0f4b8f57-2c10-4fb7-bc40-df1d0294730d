flarum.core.app.translator.addTranslations({"core.forum.change_email.incorrect_password_message":"The password you entered is incorrect.","core.forum.change_password.send_button":"Send Password Reset Email","core.forum.change_password.text":"Click the button below and check your email for a link to change your password.","core.forum.composer.close_tooltip":"Close","core.forum.composer.exit_full_screen_tooltip":"Exit Full Screen","core.forum.composer.full_screen_tooltip":"Full Screen","core.forum.composer.minimize_tooltip":"Minimize","core.forum.composer.preview_tooltip":"Preview","core.forum.composer_discussion.body_placeholder":"Write a Post...","core.forum.composer_discussion.discard_confirmation":"You have not posted your discussion. Do you wish to discard it?","core.forum.composer_discussion.submit_button":"Post Discussion","core.forum.composer_discussion.title_placeholder":"Discussion Title","core.forum.composer_edit.discard_confirmation":"You have not saved your changes. Do you wish to discard them?","core.forum.composer_edit.edited_message":"Your edit was made.","core.forum.composer_edit.post_link":"Post #{number} in {discussion}","core.forum.composer_reply.discard_confirmation":"You have not posted your reply. Do you wish to discard it?","core.forum.composer_reply.posted_message":"Your reply was posted.","core.forum.composer_reply.submit_button":"Post Reply","core.forum.discussion_controls.cannot_reply_button":"Can't Reply","core.forum.discussion_controls.cannot_reply_text":"You don't have permission to reply to this discussion.","core.forum.discussion_controls.delete_confirmation":"Are you sure you want to delete this discussion?","core.forum.discussion_controls.log_in_to_reply_button":"Log In to Reply","core.forum.discussion_controls.toggle_dropdown_accessible_label":"Toggle discussion actions dropdown menu","core.forum.discussion_list.empty_text":"It looks as though there are no discussions here.","core.forum.discussion_list.replied_text":"{username} replied {ago}","core.forum.discussion_list.started_text":"{username} started {ago}","core.forum.discussion_list.total_replies_a11y_label":"{count, plural, one {# reply} other {# replies}}","core.forum.discussion_list.unread_replies_a11y_label":"{count, plural, one {# unread reply} other {# unread replies}}. Mark unread {count, plural, one {reply} other {replies}} as read.","core.forum.forgot_password.email_sent_message":"If the email you entered is registered with this site, we'll send you an email containing a link to reset your password. Check your spam folder if you don't receive it within the next minute or two.","core.forum.forgot_password.not_found_message":"There is no user registered with that email address.","core.forum.forgot_password.submit_button":"Recover Password","core.forum.forgot_password.text":"Enter your email address and we will send you a link to reset your password.","core.forum.forgot_password.title":"Forgot Password","core.forum.header.admin_button":"Administration","core.forum.header.back_to_index_tooltip":"Back to Discussion List","core.forum.header.locale_dropdown_accessible_label":"Change forum locale","core.forum.header.profile_button":"Profile","core.forum.header.search_clear_button_accessible_label":"Clear search query","core.forum.header.search_placeholder":"Search Forum","core.forum.header.search_role_label":"Search Forum","core.forum.header.session_dropdown_accessible_label":"Toggle session options dropdown menu","core.forum.index.cannot_start_discussion_button":"Can't Start Discussion","core.forum.index.mark_all_as_read_confirmation":"Are you sure you want to mark all discussions as read?","core.forum.index.refresh_tooltip":"Refresh","core.forum.index.toggle_sidenav_dropdown_accessible_label":"Toggle navigation dropdown menu","core.forum.index_sort.latest_button":"Latest","core.forum.index_sort.newest_button":"Newest","core.forum.index_sort.oldest_button":"Oldest","core.forum.index_sort.relevance_button":"Relevance","core.forum.index_sort.toggle_dropdown_accessible_label":"Change discussion list sorting","core.forum.index_sort.top_button":"Top","core.forum.log_in.forgot_password_link":"Forgot password?","core.forum.log_in.invalid_login_message":"Your login details were incorrect.","core.forum.log_in.remember_me_label":"Remember Me","core.forum.log_in.sign_up_text":"Don't have an account? <a>Sign Up<\/a>","core.forum.log_in.username_or_email_placeholder":"Username or Email","core.forum.notifications.delete_all_confirm":"Are you sure you want to delete all notifications? This action is not reversable","core.forum.notifications.delete_all_tooltip":"Delete all notifications","core.forum.notifications.discussion_renamed_text":"{username} changed the title","core.forum.notifications.empty_text":"No Notifications","core.forum.notifications.mark_as_read_tooltip":"Mark as Read","core.forum.notifications.toggle_dropdown_accessible_label":"View notifications","core.forum.post.edited_text":"Edited","core.forum.post.edited_tooltip":"{username} edited {ago}","core.forum.post.number_tooltip":"Post #{number}","core.forum.post_controls.delete_confirmation":"Are you sure you want to delete this post forever? This action cannot be undone.","core.forum.post_controls.hide_confirmation":"Are you sure you want to delete this post?","core.forum.post_controls.toggle_dropdown_accessible_label":"Toggle post controls dropdown menu","core.forum.post_scrubber.now_link":"Now","core.forum.post_scrubber.original_post_link":"Original Post","core.forum.post_scrubber.unread_text":"{count} unread","core.forum.post_scrubber.viewing_text":"{count, plural, one {{index} of {formattedCount} post} other {{index} of {formattedCount} posts}}","core.forum.post_stream.discussion_renamed_old_tooltip":"The old title was: \"{old}\"","core.forum.post_stream.discussion_renamed_text":"{username} changed the title to {new} {time}.","core.forum.post_stream.time_lapsed_text":"{period} later","core.forum.rename_discussion.title":"Rename Discussion","core.forum.search.all_discussions_button":"Search all discussions for \"{query}\"","core.forum.security.browser_on_operating_system":"{browser} on {os}","core.forum.security.cannot_terminate_current_session":"Cannot terminate the current active session. Log out instead.","core.forum.security.created":"Created","core.forum.security.current_active_session":"Current Active Session","core.forum.security.developer_tokens_heading":"Developer Tokens","core.forum.security.empty_text":"It looks like there is nothing to see here.","core.forum.security.global_logout.heading":"Global Logout","core.forum.security.global_logout.help_text":"Clears current cookie session, terminates all sessions, revokes developer tokens, and invalidates any email confirmation or password reset emails.","core.forum.security.hide_access_token":"Hide Token","core.forum.security.last_activity":"Last activity","core.forum.security.new_access_token_modal.submit_button":"Create Token","core.forum.security.new_access_token_modal.title_placeholder":"Title","core.forum.security.revoke_access_token":"Revoke","core.forum.security.sessions_heading":"Active Sessions","core.forum.security.session_terminated":"{count, plural, one {Session terminated} other {# Sessions terminated}}.","core.forum.security.session_termination_failed":"An error occurred while terminating your sessions.","core.forum.security.show_access_token":"View Token","core.forum.security.terminate_all_other_sessions":"Terminate all other sessions","core.forum.security.terminate_session":"Terminate","core.forum.security.token_revoked":"Token revoked.","core.forum.security.token_item_title":"{title} - {token}","core.forum.security.token_title_placeholder":"\/","core.forum.settings.account_heading":"Account","core.forum.settings.notification_checkbox_a11y_label_template":"Receive \"{description}\" notifications via {method}","core.forum.settings.notify_by_web_heading":"Web","core.forum.settings.notify_discussion_renamed_label":"Someone renames a discussion I started","core.forum.settings.privacy_disclose_online_label":"Allow others to see when I am online","core.forum.settings.privacy_heading":"Privacy","core.forum.sign_up.log_in_text":"Already have an account? <a>Log In<\/a>","core.forum.sign_up.welcome_text":"Welcome, {username}!","core.forum.user.avatar_upload_button":"Upload","core.forum.user.avatar_upload_tooltip":"Upload a new avatar","core.forum.user.in_discussion_text":"In {discussion}","core.forum.user.joined_date_text":"Joined {ago}","core.forum.user.online_text":"Online","core.forum.user.posts_empty_text":"It looks like there are no posts here.","core.forum.user_controls.button":"Controls","core.forum.user_controls.delete_confirmation":"Are you sure you want to delete this user? The user's posts will NOT be deleted.","core.forum.user_controls.delete_error_message":"Deletion of user <i>{username} ({email})<\/i> failed","core.forum.user_controls.delete_success_message":"User <i>{username} ({email})<\/i> was deleted","core.forum.user_controls.toggle_dropdown_accessible_label":"Toggle user controls dropdown menu","core.forum.user_email_confirmation.resend_button":"Resend Confirmation Email","core.forum.user_email_confirmation.sent_message":"Sent","core.forum.welcome_hero.hide":"Hide welcome message","core.lib.debug_button":"Debug","core.lib.alert.dismiss_a11y_label":"Dismiss alert","core.lib.badge.hidden_tooltip":"Hidden","core.lib.dropdown.toggle_dropdown_accessible_label":"Toggle dropdown menu","core.lib.data_segment.label":"{label}:","core.lib.edit_user.activate_button":"Activate User","core.lib.edit_user.groups_heading":"Groups","core.lib.edit_user.set_password_label":"Set new password","core.lib.edit_user.nothing_available":"You are not allowed to edit this user.","core.lib.error.circular_dependencies_message":"Circular dependencies detected: {extensions}. Aborting. Please disable one of the extensions and try again.","core.lib.error.dependent_extensions_message":"Cannot disable {extension} until the following dependent extensions are disabled: {extensions}","core.lib.error.extension_initialiation_failed_message":"{extension} failed to initialize, check the browser console for further information.","core.lib.error.generic_message":"Oops! Something went wrong. Please reload the page and try again.","core.lib.error.generic_cross_origin_message":"Oops! Something went wrong during a cross-origin request. Please reload the page and try again.","core.lib.error.missing_dependencies_message":"Cannot enable {extension} until the following dependencies are enabled: {extensions}","core.lib.error.not_found_message":"The requested resource was not found.","core.lib.error.payload_too_large_message":"The request payload was too large.","core.lib.error.permission_denied_message":"You do not have permission to do that.","core.lib.error.rate_limit_exceeded_message":"You're going a little too quickly. Please try again in a few seconds.","core.lib.error.render_failed_message":"Sorry, we encountered an error while displaying this content. If you're a user, please try again later. If you're an administrator, take a look in your Flarum log files for more information.","core.lib.meta_titles.with_page_title":"{pageNumber, plural, =1 {{pageTitle} - {forumName}} other {{pageTitle}: Page # - {forumName}}}","core.lib.meta_titles.without_page_title":"{pageNumber, plural, =1 {{forumName}} other {Page # - {forumName}}}","core.lib.modal.close":"Close","core.lib.nav.drawer_button":"Open Navigation Drawer","core.lib.number_suffix.kilo_text":"K","core.lib.number_suffix.mega_text":"M","core.lib.datetime_formats.humanTimeShort":"D MMM","core.lib.datetime_formats.humanTimeLong":"ll","core.lib.datetime_formats.scrubber":"MMMM YYYY","core.lib.series.glue_text":", ","core.lib.series.three_text":"{first}, {second}, and {third}","core.lib.series.two_text":"{first} and {second}","core.lib.username.deleted_text":"[deleted]","flarum-flags.forum.flag_post.confirmation_message":"Thank you for flagging this post. Our moderators will look into it.","flarum-flags.forum.flag_post.reason_details_placeholder":"Additional details (optional)","flarum-flags.forum.flag_post.reason_inappropriate_label":"Inappropriate","flarum-flags.forum.flag_post.reason_inappropriate_text":"This post is offensive, abusive, or violates our <a>community guidelines<\/a>.","flarum-flags.forum.flag_post.reason_missing_message":"Please provide some details for our moderators.","flarum-flags.forum.flag_post.reason_off_topic_label":"Off-topic","flarum-flags.forum.flag_post.reason_off_topic_text":"This post is not relevant to the current discussion and should be moved elsewhere.","flarum-flags.forum.flag_post.reason_other_label":"Other (please specify)","flarum-flags.forum.flag_post.reason_spam_label":"Spam","flarum-flags.forum.flag_post.reason_spam_text":"This post is an advertisement.","flarum-flags.forum.flagged_posts.empty_text":"No Flags","flarum-flags.forum.flagged_posts.item_text":"{username} in <em>{discussion}<\/em>","flarum-flags.forum.post.dismiss_flag_button":"Dismiss Flag","flarum-flags.forum.post.flagged_by_text":"Flagged by {username}","flarum-flags.forum.post.flagged_by_with_reason_text":"Flagged by {username} as {reason}","flarum-flags.forum.post_controls.flag_button":"Flag","flarum-approval.forum.post_controls.approve_button":"Approve","flarum-tags.forum.all_tags.meta_description_text":"All Tags","flarum-tags.forum.choose_tags.edit_title":"Edit Tags for {title}","flarum-tags.forum.choose_tags.title":"Choose Tags for Your Discussion","flarum-tags.forum.discussion_controls.edit_tags_button":"Edit Tags","flarum-tags.forum.header.back_to_tags_tooltip":"Back to Tag List","flarum-tags.forum.index.more_link":"More...","flarum-tags.forum.index.untagged_link":"Untagged","flarum-tags.forum.post_stream.added_and_removed_tags_text":"{username} added the {tagsAdded} and removed the {tagsRemoved} {time}.","flarum-tags.forum.post_stream.added_tags_text":"{username} added the {tagsAdded} {time}.","flarum-tags.forum.post_stream.removed_tags_text":"{username} removed the {tagsRemoved} {time}.","flarum-tags.forum.post_stream.tags_text":"{count, plural, one {{tags} tag} other {{tags} tags}}","flarum-tags.forum.tag.meta_description_text":"All discussions with the {tag} tag","flarum-tags.lib.deleted_tag_text":"Deleted","flarum-tags.lib.tag_selection_modal.bypass_requirements":"Bypass tag requirements","flarum-tags.lib.tag_selection_modal.choose_primary_placeholder":"{count, plural, one {Choose a primary tag} other {Choose # primary tags}}","flarum-suspend.forum.notifications.user_suspended_text":"You have been suspended for {timeReadable}","flarum-suspend.forum.notifications.user_suspended_indefinite_text":"You have been suspended indefinitely","flarum-suspend.forum.notifications.user_unsuspended_text":"You have been unsuspended","flarum-suspend.forum.suspension_info.dismiss_button":"Dismiss","flarum-suspend.forum.suspension_info.indefinite":"This is an indefinite suspension","flarum-suspend.forum.suspension_info.limited":"This suspension will be in force until {date}","flarum-suspend.forum.suspension_info.title":"This account is suspended","flarum-suspend.forum.suspend_user.display_message":"Display message for user","flarum-suspend.forum.suspend_user.indefinitely_label":"Suspended indefinitely","flarum-suspend.forum.suspend_user.limited_time_days_text":" days","flarum-suspend.forum.suspend_user.limited_time_label":"Suspended for a limited time...","flarum-suspend.forum.suspend_user.not_suspended_label":"Not suspended","flarum-suspend.forum.suspend_user.placeholder_optional":"Optional","flarum-suspend.forum.suspend_user.reason":"Reason for suspension","flarum-suspend.forum.suspend_user.status_heading":"Suspension Status","flarum-suspend.forum.suspend_user.title":"Suspend {username}","flarum-suspend.forum.user_badge.suspended_tooltip":"Suspended","flarum-suspend.forum.user_controls.suspend_button":"Suspend","flarum-subscriptions.forum.discussion_controls.unfollow_button":"Unfollow","flarum-subscriptions.forum.discussion_controls.unignore_button":"Unignore","flarum-subscriptions.forum.notifications.new_post_text":"{username} posted","flarum-subscriptions.forum.settings.follow_after_reply_label":"Automatically follow discussions that I reply to","flarum-subscriptions.forum.settings.notify_for_all_posts_label":"Notify about every new post instead of only the last in a discussion","flarum-subscriptions.forum.settings.notify_new_post_label":"Someone posts in a discussion I'm following","flarum-subscriptions.forum.sub_controls.following_text":"Be notified of all replies.","flarum-subscriptions.forum.sub_controls.ignoring_text":"Never be notified. Hide from the discussion list.","flarum-subscriptions.forum.sub_controls.not_following_button":"Not Following","flarum-subscriptions.forum.sub_controls.not_following_text":"Be notified only when @mentioned.","flarum-subscriptions.forum.sub_controls.notify_alert_tooltip":"Get a forum notification when there are new posts","flarum-subscriptions.forum.sub_controls.notify_email_tooltip":"Get an email when there are new posts","flarum-sticky.forum.discussion_controls.unsticky_button":"Unsticky","flarum-sticky.forum.post_stream.discussion_stickied_text":"{username} stickied the discussion {time}.","flarum-sticky.forum.post_stream.discussion_unstickied_text":"{username} unstickied the discussion {time}.","flarum-mentions.forum.composer.mention_tooltip":"Mention a user, group or post","flarum-mentions.forum.composer.reply_to_post_text":"Reply to #{number}","flarum-mentions.forum.mentioned_by.title":"Replies to this post","flarum-mentions.forum.notifications.post_mentioned_text":"{username} replied to your post","flarum-mentions.forum.notifications.user_mentioned_text":"{username} mentioned you","flarum-mentions.forum.notifications.group_mentioned_text":"{username} mentioned a group you're a member of","flarum-mentions.forum.post.mentioned_by_more_text":"{count} more replies.","flarum-mentions.forum.post.mentioned_by_self_text":"{users} replied to this.","flarum-mentions.forum.post.mentioned_by_text":"{users} replied to this.","flarum-mentions.forum.post.quote_button":"Quote","flarum-mentions.forum.settings.notify_post_mentioned_label":"Someone replies to one of my posts","flarum-mentions.forum.settings.notify_user_mentioned_label":"Someone mentions me in a post","flarum-mentions.forum.settings.notify_group_mentioned_label":"Someone mentions a group I'm a member of in a post","flarum-mentions.forum.user.mentions_link":"Mentions","flarum-mentions.forum.post_mention.deleted_text":"[unknown]","flarum-mentions.forum.group_mention.deleted_text":"[unknown group]","flarum-markdown.lib.composer.bold_tooltip":"Add bold text","flarum-markdown.lib.composer.code_tooltip":"Insert code","flarum-markdown.lib.composer.header_tooltip":"Add header text","flarum-markdown.lib.composer.image_tooltip":"Add an image","flarum-markdown.lib.composer.italic_tooltip":"Add italic text","flarum-markdown.lib.composer.link_tooltip":"Add a link","flarum-markdown.lib.composer.ordered_list_tooltip":"Add a numbered list","flarum-markdown.lib.composer.quote_tooltip":"Insert a quote","flarum-markdown.lib.composer.spoiler_tooltip":"Insert a spoiler","flarum-markdown.lib.composer.strikethrough_tooltip":"Add strikethrough text","flarum-markdown.lib.composer.unordered_list_tooltip":"Add a bulleted list","flarum-lock.forum.badge.locked_tooltip":"Locked","flarum-lock.forum.discussion_controls.lock_button":"Lock","flarum-lock.forum.discussion_controls.unlock_button":"Unlock","flarum-lock.forum.notifications.discussion_locked_text":"{username} locked","flarum-lock.forum.post_stream.discussion_locked_text":"{username} locked the discussion {time}.","flarum-lock.forum.post_stream.discussion_unlocked_text":"{username} unlocked the discussion {time}.","flarum-lock.forum.settings.notify_discussion_locked_label":"Someone locks a discussion I started","flarum-likes.forum.notifications.post_liked_text":"{username} liked your post","flarum-likes.forum.post.like_link":"Like","flarum-likes.forum.post.liked_by_self_text":"{users} like this.","flarum-likes.forum.post.liked_by_text":"{count, plural, one {{users} likes this} other {{users} like this}}.","flarum-likes.forum.post.unlike_link":"Unlike","flarum-likes.forum.post_likes.title":"Users Who Like This","flarum-likes.forum.settings.notify_post_liked_label":"Someone likes one of my posts","flarum-likes.forum.user.likes_link":"Likes","flarum-emoji.forum.composer.emoji_tooltip":"Insert emoji","flarum-emoji.forum.composer.type_to_search_text":"Type to search for an emoji","flarum-bbcode.forum.quote.wrote":"wrote","core.forum.change_email.confirm_password_placeholder":"Confirm Password","core.forum.change_email.confirmation_message":"We've sent a confirmation email to {email}. If it doesn't arrive soon, check your spam folder.","core.forum.change_email.dismiss_button":"OK","core.forum.change_email.submit_button":"Save Changes","core.forum.change_email.title":"Change Email","core.forum.change_password.title":"Change Password","core.forum.composer_discussion.title":"Start a Discussion","core.forum.composer_edit.submit_button":"Save Changes","core.forum.composer_edit.view_button":"View","core.forum.composer_reply.body_placeholder":"Write a Reply...","core.forum.composer_reply.view_button":"View","core.forum.discussion_controls.delete_button":"Delete","core.forum.discussion_controls.delete_forever_button":"Delete Forever","core.forum.discussion_controls.rename_button":"Rename","core.forum.discussion_controls.reply_button":"Reply","core.forum.discussion_controls.restore_button":"Restore","core.forum.discussion_list.load_more_button":"Load More","core.forum.forgot_password.dismiss_button":"OK","core.forum.forgot_password.email_placeholder":"Email","core.forum.header.log_in_link":"Log In","core.forum.header.log_out_button":"Log Out","core.forum.header.settings_button":"Settings","core.forum.header.sign_up_link":"Sign Up","core.forum.index.all_discussions_link":"All Discussions","core.forum.index.mark_all_as_read_tooltip":"Mark All as Read","core.forum.index.meta_title_text":"All Discussions","core.forum.index.start_discussion_button":"Start a Discussion","core.forum.log_in.password_placeholder":"Password","core.forum.log_in.submit_button":"Log In","core.forum.log_in.title":"Log In","core.forum.notifications.mark_all_as_read_tooltip":"Mark All as Read","core.forum.notifications.title":"Notifications","core.forum.notifications.tooltip":"Notifications","core.forum.post_controls.delete_button":"Delete","core.forum.post_controls.delete_forever_button":"Delete Forever","core.forum.post_controls.edit_button":"Edit","core.forum.post_controls.restore_button":"Restore","core.forum.post_stream.load_more_button":"Load More","core.forum.post_stream.reply_placeholder":"Write a Reply...","core.forum.rename_discussion.submit_button":"Rename","core.forum.search.discussions_heading":"Discussions","core.forum.search.users_heading":"Users","core.forum.security.global_logout.log_out_button":"Log Out","core.forum.security.never":"Never","core.forum.security.new_access_token_button":"New Token","core.forum.security.new_access_token_modal.title":"New Token","core.forum.security.revoke_access_token_confirmation":"Are you sure you want to proceed? This action cannot be undone.","core.forum.security.terminate_all_other_sessions_confirmation":"Are you sure you want to proceed? This action cannot be undone.","core.forum.security.title":"Security","core.forum.settings.change_email_button":"Change Email","core.forum.settings.change_password_button":"Change Password","core.forum.settings.notifications_heading":"Notifications","core.forum.settings.notify_by_email_heading":"Email","core.forum.settings.title":"Settings","core.forum.sign_up.dismiss_button":"OK","core.forum.sign_up.email_placeholder":"Email","core.forum.sign_up.password_placeholder":"Password","core.forum.sign_up.submit_button":"Sign Up","core.forum.sign_up.title":"Sign Up","core.forum.sign_up.username_placeholder":"Username","core.forum.user.avatar_remove_button":"Remove","core.forum.user.discussions_link":"Discussions","core.forum.user.posts_link":"Posts","core.forum.user.posts_load_more_button":"Load More","core.forum.user.security_link":"Security","core.forum.user.settings_link":"Settings","core.forum.user_controls.delete_button":"Delete","core.forum.user_controls.edit_button":"Edit","core.forum.user_email_confirmation.alert_message":"We've sent a confirmation email to {email}. If it doesn't arrive soon, check your spam folder.","core.lib.edit_user.email_heading":"Email","core.lib.edit_user.email_label":"Email","core.lib.edit_user.password_heading":"Password","core.lib.edit_user.password_label":"Password","core.lib.edit_user.submit_button":"Save Changes","core.lib.edit_user.title":"Edit User","core.lib.edit_user.username_heading":"Username","core.lib.edit_user.username_label":"Username","core.lib.loading_indicator.accessible_label":"Loading...","flarum-flags.forum.flag_post.dismiss_button":"OK","flarum-flags.forum.flag_post.submit_button":"Flag Post","flarum-flags.forum.flag_post.title":"Flag Post","flarum-flags.forum.flagged_posts.title":"Flagged Posts","flarum-flags.forum.flagged_posts.tooltip":"Flagged Posts","flarum-approval.forum.badge.awaiting_approval_tooltip":"Awaiting approval","flarum-approval.forum.post.awaiting_approval_text":"Awaiting approval","flarum-tags.forum.all_tags.meta_title_text":"Tags","flarum-tags.forum.composer_discussion.choose_tags_link":"Choose Tags","flarum-tags.forum.index.tags_link":"Tags","flarum-tags.lib.tag_selection_modal.choose_secondary_placeholder":"{count, plural, one {Choose 1 more tag} other {Choose # more tags}}","flarum-tags.lib.tag_selection_modal.choose_tags_placeholder":"{count, plural, one {Choose 1 more tag} other {Choose # more tags}}","flarum-tags.lib.tag_selection_modal.submit_button":"OK","flarum-tags.lib.tag_selection_modal.title":"Choose Tags","flarum-suspend.forum.suspend_user.submit_button":"Save Changes","flarum-subscriptions.forum.badge.following_tooltip":"Following","flarum-subscriptions.forum.badge.ignoring_tooltip":"Ignoring","flarum-subscriptions.forum.discussion_controls.follow_button":"Follow","flarum-subscriptions.forum.following.meta_title_text":"Following","flarum-subscriptions.forum.index.following_link":"Following","flarum-subscriptions.forum.sub_controls.follow_button":"Follow","flarum-subscriptions.forum.sub_controls.following_button":"Following","flarum-subscriptions.forum.sub_controls.ignoring_button":"Ignoring","flarum-sticky.forum.badge.sticky_tooltip":"Sticky","flarum-sticky.forum.discussion_controls.sticky_button":"Sticky","flarum-mentions.forum.mentioned_by.load_more_button":"Load More","flarum-mentions.forum.notifications.others_text":"{count, plural, one {# other} other {# others}}","flarum-mentions.forum.post.others_text":"{count, plural, one {# other} other {# others}}","flarum-mentions.forum.post.reply_link":"Reply","flarum-mentions.forum.post.you_text":"You","flarum-likes.forum.notifications.others_text":"{count, plural, one {# other} other {# others}}","flarum-likes.forum.post.others_link":"{count, plural, one {# other} other {# others}}","flarum-likes.forum.post.you_text":"You","flarum-likes.forum.post_likes.load_more_button":"Load More"})

//# sourceMappingURL=http://flarum.test/assets/forum-en.js.map