{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "CallExpression", "path", "file", "get", "matchesPattern", "node", "callee", "addHelper"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-object-assign\",\n\n    visitor: {\n      CallExpression: function (path, file) {\n        if (path.get(\"callee\").matchesPattern(\"Object.assign\")) {\n          path.node.callee = file.addHelper(\"extends\");\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEtC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,yBAAyB;IAE/BC,OAAO,EAAE;MACPC,cAAc,EAAE,SAAAA,CAAUC,IAAI,EAAEC,IAAI,EAAE;QACpC,IAAID,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC,CAACC,cAAc,CAAC,eAAe,CAAC,EAAE;UACtDH,IAAI,CAACI,IAAI,CAACC,MAAM,GAAGJ,IAAI,CAACK,SAAS,CAAC,SAAS,CAAC;QAC9C;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}