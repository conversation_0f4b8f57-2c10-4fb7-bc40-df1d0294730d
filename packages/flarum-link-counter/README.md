# Link Click Tracker for Flarum

A comprehensive Flarum extension that tracks clicks on external links within forum posts, similar to Discourse's link click counters. This extension provides detailed analytics while maintaining user privacy and performance.

## Features

### Core Functionality
- **Automatic External Link Detection**: Automatically identifies and tracks clicks on links that point outside your forum domain
- **Non-intrusive Click Counters**: Displays small, unobtrusive click counts next to tracked links
- **Rate Limiting**: Prevents spam by limiting one click count per user per link within a configurable time window (default: 24 hours)
- **Guest Support**: Tracks clicks from both registered users and guests
- **Real-time Updates**: Click counters update immediately after successful tracking

### Security & Performance
- **URL Validation**: Comprehensive URL validation and sanitization to prevent XSS attacks
- **CSRF Protection**: All API endpoints are protected against CSRF attacks
- **Database Optimization**: Proper indexing for high-performance queries
- **Rate Limiting Middleware**: API-level rate limiting to prevent abuse
- **Input Sanitization**: All user inputs are properly validated and sanitized

### Admin Configuration
- **Enable/Disable Toggle**: Easily enable or disable link tracking site-wide
- **Rate Limit Configuration**: Customize the time window for rate limiting (1-168 hours)
- **User Group Filtering**: Optionally restrict tracking to specific user groups
- **Performance Settings**: Configure tracking parameters for optimal performance

### Privacy & Compliance
- **GDPR Considerations**: Designed with privacy in mind
- **Configurable Tracking**: Admins can control which users are tracked
- **Data Retention**: Automatic cleanup of old tracking data (configurable)
- **Transparent Operation**: Users can see click counts, promoting transparency

## Installation

1. **Download the Extension**
   ```bash
   composer require datlechin/flarum-link-click-tracker
   ```

2. **Enable the Extension**
   ```bash
   php flarum migrate
   php flarum cache:clear
   ```

3. **Configure Settings**
   - Go to Admin Panel → Extensions → Link Click Tracker
   - Configure your preferred settings
   - Save changes

## Configuration

### Admin Settings

#### Basic Settings
- **Enable Link Click Tracking**: Master toggle for the entire extension
- **Rate Limit (Hours)**: Time window for rate limiting (default: 24 hours)
- **Allowed User Groups**: Comma-separated list of group IDs that can have their clicks tracked

#### Advanced Configuration
The extension respects Flarum's permission system and integrates seamlessly with existing user groups and permissions.

### Database Schema

The extension creates a `link_clicks` table with the following structure:

```sql
CREATE TABLE link_clicks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    url TEXT NOT NULL,
    user_id INT UNSIGNED NULL,
    post_id INT UNSIGNED NOT NULL,
    clicked_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    
    INDEX idx_url_post (url(255), post_id),
    INDEX idx_user_url_post_time (user_id, url(255), post_id, clicked_at),
    INDEX idx_post_time (post_id, clicked_at),
    INDEX idx_clicked_at (clicked_at)
);
```

## API Endpoints

### Create Link Click
```
POST /api/link-clicks
```

**Request Body:**
```json
{
  "data": {
    "type": "link-clicks",
    "attributes": {
      "url": "https://example.com",
      "postId": 123
    }
  }
}
```

### Get Post Link Clicks
```
GET /api/posts/{id}/link-clicks
```

**Response:**
```json
{
  "data": [
    {
      "type": "link-clicks",
      "id": "unique-id",
      "attributes": {
        "url": "https://example.com",
        "postId": 123,
        "clickCount": 5
      }
    }
  ]
}
```

## Technical Implementation

### Backend (PHP)
- **Model**: `LinkClick` - Eloquent model with relationships to User and Post
- **Controllers**: RESTful API controllers for creating and retrieving click data
- **Commands**: CQRS pattern implementation for business logic
- **Middleware**: Rate limiting and security middleware
- **Migrations**: Database schema with proper indexing

### Frontend (TypeScript/Mithril.js)
- **Link Detection**: Automatic detection of external links using URL parsing
- **Event Handling**: Non-blocking click event interception
- **UI Components**: Lightweight click counter components
- **API Integration**: Seamless integration with Flarum's API layer
- **Error Handling**: Graceful degradation when tracking fails

### Performance Optimizations
- **Database Indexing**: Optimized indexes for fast queries
- **Caching**: Intelligent caching of click counts
- **Lazy Loading**: Click counts loaded asynchronously
- **Batch Processing**: Efficient handling of multiple links per post

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This extension is licensed under the MIT License. See the LICENSE file for details.

## Support

- **Issues**: Report bugs and feature requests on GitHub
- **Documentation**: Comprehensive documentation available in the repository
- **Community**: Get help from the Flarum community

## Changelog

### Version 1.0.0
- Initial release
- Core link tracking functionality
- Admin configuration panel
- Rate limiting and security features
- Mobile-responsive design
- GDPR compliance considerations

## Credits

Developed by [Ngô Quốc Đạt](https://github.com/datlechin) for the Flarum community.

Inspired by Discourse's link click tracking feature, adapted for Flarum's architecture and design principles.
